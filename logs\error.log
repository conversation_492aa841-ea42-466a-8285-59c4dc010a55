2025-07-11 16:36:00 - __main__ - ERROR - _show_task_list:4518 - Error showing task list: Can't parse entities: can't find end of the entity starting at byte offset 132
2025-07-11 16:41:20 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:41:23 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:50:06 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:56:53 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:56:58 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:56:59 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:04 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:05 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:11 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:15 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:18 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:22 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:25 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:30 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:36 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:39 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:47 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:51 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:58:03 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:58:08 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:30 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:35 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:36 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:40 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:42 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:47 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:49 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:57 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:03 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:12 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:17 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:27:03 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:03 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:04 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:04 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:05 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:05 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:07 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:31:57 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:00 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:00 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:06 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:32:10 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:32:18 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:15:59 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:03 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:04 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:11 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:17 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:19 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:23 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:26 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:32 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:38 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:46 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:17:06 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:17:11 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:19:53 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:19:58 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:20:24 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:20:28 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:49 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:53 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:59 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:01 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:06 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:09 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:12 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:16 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:22 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:27 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:35 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:44 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:56 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:00 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:17 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:22 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:48 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:52 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:23 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:27 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:58 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:02 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:33 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:37 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:08 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:12 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:42 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:47 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:17 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:21 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:52 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:56 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:56:27 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:56:31 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:02 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:06 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:37 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:41 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:11 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:16 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:46 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:50 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:21 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:25 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:56 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:00 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:31 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:35 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:05 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:10 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:40 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:44 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:15 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:19 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:50 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:03:25 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:03:29 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:00 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:04 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:34 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:39 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:09 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:13 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:44 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:48 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:06:18 - __main__ - ERROR - handle_photo:791 - Error handling photo: 'NoneType' object has no attribute 'id'
2025-07-12 07:06:18 - __main__ - ERROR - error_handler:6445 - Update Update(channel_post=Message(caption='💰 DAILY CRYPTO UPDATE: 12-07-2025\n\nMajor Cryptocurrencies:\n\n- 🟠 BTC: $117,703.00 | 24h: +1.53% 📈\n\n- 🔷 ETH: $2,965.74 | 24h: +0.59% 📈\n\n- 🟣 SOL: $163.47 | 24h: -0.63% 📉\n\n- 💎 TON: $2.98 | 24h: +1.20% 📈\n\nMarket Highlights:\n\n- 🔥 Trending: #TON at $2.98 | 24h: +1.20%\n\n- 📈 Top Gainer: #M at $0.80 | 24h: +81.18%\n\n- 📉 Top Loser: #BONK at $0.00 | 24h: -6.73%', caption_entities=(MessageEntity(length=31, offset=3, type=<MessageEntityType.BOLD>), MessageEntity(length=23, offset=36, type=<MessageEntityType.ITALIC>), MessageEntity(length=36, offset=63, type=<MessageEntityType.ITALIC>), MessageEntity(length=34, offset=103, type=<MessageEntityType.ITALIC>), MessageEntity(length=32, offset=141, type=<MessageEntityType.ITALIC>), MessageEntity(length=30, offset=177, type=<MessageEntityType.ITALIC>), MessageEntity(length=18, offset=209, type=<MessageEntityType.ITALIC>), MessageEntity(length=13, offset=231, type=<MessageEntityType.ITALIC>), MessageEntity(length=4, offset=244, type=<MessageEntityType.HASHTAG>), MessageEntity(length=4, offset=244, type=<MessageEntityType.ITALIC>), MessageEntity(length=23, offset=248, type=<MessageEntityType.ITALIC>), MessageEntity(length=15, offset=275, type=<MessageEntityType.ITALIC>), MessageEntity(length=2, offset=290, type=<MessageEntityType.HASHTAG>), MessageEntity(length=2, offset=290, type=<MessageEntityType.ITALIC>), MessageEntity(length=24, offset=292, type=<MessageEntityType.ITALIC>), MessageEntity(length=14, offset=320, type=<MessageEntityType.ITALIC>), MessageEntity(length=5, offset=334, type=<MessageEntityType.HASHTAG>), MessageEntity(length=5, offset=334, type=<MessageEntityType.ITALIC>), MessageEntity(length=23, offset=339, type=<MessageEntityType.ITALIC>)), channel_chat_created=False, chat=Chat(id=-1001675894903, title="Dev's Crypto Channel 🪐", type=<ChatType.CHANNEL>, username='crypto_by_dev'), date=datetime.datetime(2025, 7, 12, 1, 36, 17, tzinfo=<UTC>), delete_chat_photo=False, group_chat_created=False, message_id=220, photo=(PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADcwADNgQ', file_size=1808, file_unique_id='AQADssgxG-iwUFd4', height=90, width=90), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADbQADNgQ', file_size=22519, file_unique_id='AQADssgxG-iwUFdy', height=320, width=320), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADeAADNgQ', file_size=82164, file_unique_id='AQADssgxG-iwUFd9', height=800, width=800), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADeQADNgQ', file_size=104421, file_unique_id='AQADssgxG-iwUFd-', height=1024, width=1024)), sender_chat=Chat(id=-1001675894903, title="Dev's Crypto Channel 🪐", type=<ChatType.CHANNEL>, username='crypto_by_dev'), supergroup_chat_created=False), update_id=388447795) caused error 'NoneType' object has no attribute 'reply_text'
2025-07-12 07:06:19 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:06:23 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 08:31:10 - __main__ - ERROR - initialize_async_components:84 - ❌ Failed to initialize async components: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 08:31:10 - __main__ - ERROR - main:6531 - ❌ Bot error: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 09:14:08 - __main__ - ERROR - initialize_async_components:90 - ❌ Failed to initialize async components: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 09:14:08 - __main__ - ERROR - main:6571 - ❌ Bot error: 'NoneType' object has no attribute 'admin_settings'
