# Telegram Referral Bot Setup Guide 🚀

This guide will help you set up and deploy the Telegram Referral Earning Bot with all its features.

## 📋 Prerequisites

### Required Software
- **Python 3.8+** - Programming language runtime
- **MongoDB Atlas Account** - Cloud database service
- **Telegram <PERSON>t Token** - From @BotFather

### Required Accounts
1. **Telegram Account** - To create and manage the bot
2. **MongoDB Atlas** - Free tier available for database

**Note:** No domain, SSL certificates, or public IP required - the bot uses long polling!

## 🛠️ Installation Steps

### 1. Clone and Setup Project

```bash
# Clone the repository
git clone <repository-url>
cd refer-earn-bot

# Make deployment script executable
chmod +x deploy.py

# Run automated deployment
python deploy.py
```

### 2. Create Telegram Bot

1. **Message @BotFather** on Telegram
2. **Create new bot** with `/newbot`
3. **Choose bot name** (e.g., "My Referral Bot")
4. **Choose username** (e.g., "my_referral_bot")
5. **Save the bot token** provided by BotFather

### 3. Setup MongoDB Atlas

1. **Create account** at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. **Create new cluster** (free tier M0)
3. **Create database user** with read/write permissions
4. **Whitelist IP addresses** (0.0.0.0/0 for all IPs)
5. **Get connection string** from "Connect" → "Connect your application"

### 4. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

**Required Configuration:**
```env
# Bot Configuration
BOT_TOKEN=your_telegram_bot_token_here
BOT_USERNAME=your_bot_username

# Database Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/database

# Admin Configuration
ADMIN_USER_IDS=*********,*********
ADMIN_PASSWORD=your_secure_admin_password

# Bot Settings (Optional - defaults provided)
REFERRAL_REWARD=10
DAILY_BONUS_AMOUNT=5
MINIMUM_WITHDRAWAL=500
```

### 5. Get Your Telegram User ID

**Method 1: Using @userinfobot**
1. Message @userinfobot on Telegram
2. Copy your user ID
3. Add to `ADMIN_USER_IDS` in .env

**Method 2: Using @RawDataBot**
1. Message @RawDataBot on Telegram
2. Find "id" field in the response
3. Add to `ADMIN_USER_IDS` in .env

## 🚀 Running the Bot

### Long Polling Mode (Default)

```bash
# Start the bot
python main.py

# The bot will start in long polling mode
# No additional configuration required
# Check logs for any errors
```

**Advantages of Long Polling:**
- ✅ No domain or SSL certificates required
- ✅ Works behind NAT/firewall
- ✅ Simple setup and deployment
- ✅ Perfect for development and production
- ✅ Automatically handles connection management

## 🔧 Advanced Configuration

### Long Polling Configuration

The bot is configured for optimal long polling performance:

```python
# Polling parameters (automatically configured)
poll_interval=1.0      # Check for updates every second
timeout=10             # Timeout for each request
read_timeout=20        # Read timeout
write_timeout=20       # Write timeout
connect_timeout=20     # Connection timeout
```

**No additional server configuration required!**

### Systemd Service (Auto-start)

```bash
# Create service file
sudo nano /etc/systemd/system/referral-bot.service
```

```ini
[Unit]
Description=Telegram Referral Bot
After=network.target

[Service]
Type=simple
User=ubuntu
WorkingDirectory=/path/to/bot
ExecStart=/usr/bin/python3 main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable referral-bot
sudo systemctl start referral-bot

# Check status
sudo systemctl status referral-bot
```

## 📊 Initial Setup Tasks

### 1. Test Bot Functionality

```bash
# Start your bot
# Send /start command
# Test all menu options
# Verify database connections
```

### 2. Configure Admin Panel

```bash
# Send /admin command to your bot
# Enter admin password
# Explore admin features
# Add initial products
```

### 3. Setup Required Channels (Optional)

1. **Create Telegram channels** for forced subscription
2. **Add your bot as admin** to each channel
3. **Use admin panel** to add channels to the bot
4. **Test channel verification** with a test account

### 4. Add Digital Products

```bash
# Use admin panel to add products:
# - Canva Pro (💎500)
# - Spotify Premium (💎500)
# - Netflix Premium (💎500)
# - Adobe Creative Cloud (💎500)
```

## 🔍 Troubleshooting

### Common Issues

**Bot Token Invalid**
```bash
# Verify token in .env file
# Check for extra spaces or characters
# Regenerate token from @BotFather if needed
```

**Database Connection Failed**
```bash
# Check MongoDB Atlas connection string
# Verify database user permissions
# Check IP whitelist settings
# Test connection with MongoDB Compass
```

**Admin Access Denied**
```bash
# Verify your user ID in ADMIN_USER_IDS
# Check admin password in .env
# Ensure no extra spaces in configuration
```

**Bot Not Receiving Messages**
```bash
# Check internet connection
# Verify bot token is correct
# Check if bot is blocked by Telegram
# Review bot logs for connection errors
```

### Log Files

```bash
# Check main log
tail -f logs/bot.log

# Check error log
tail -f logs/error.log

# Check user actions
tail -f logs/user_actions.log

# Check admin actions
tail -f logs/admin_actions.log
```

## 📈 Monitoring and Maintenance

### Regular Tasks

1. **Monitor logs** for errors and issues
2. **Check database** performance and storage
3. **Update bot** with new features
4. **Backup database** regularly
5. **Monitor user** activity and feedback

### Performance Optimization

1. **Database indexing** - Already configured
2. **Rate limiting** - Built-in protection
3. **Error handling** - Comprehensive coverage
4. **Logging** - Detailed for debugging

## 🔒 Security Best Practices

1. **Strong admin password** - Use complex passwords
2. **Regular updates** - Keep dependencies updated
3. **Monitor access** - Check admin action logs
4. **Backup data** - Regular database backups
5. **SSL certificates** - Always use HTTPS in production

## 📞 Support

### Getting Help

1. **Check logs** first for error details
2. **Review documentation** for configuration
3. **Test with minimal setup** to isolate issues
4. **Create GitHub issues** for bugs or features

### Useful Commands

```bash
# Check bot status
python -c "from src.bot import ReferralBot; print('Bot configuration OK')"

# Test database connection
python -c "from src.database import Database; import asyncio; asyncio.run(Database().connect())"

# Validate configuration
python -c "from config import Config; Config.validate_config(); print('Config OK')"
```

## 🎉 You're Ready!

Your Telegram Referral Earning Bot is now set up and ready to use! 

**Next Steps:**
1. Share your bot with friends to test referral system
2. Monitor user activity through admin panel
3. Customize messages and rewards as needed
4. Scale up as your user base grows

**Happy earning! 💰**
