#!/usr/bin/env python3
"""
Test script to verify referral data consistency between balance profile and referrals section
"""

import asyncio
import sys
import os
from datetime import datetime, timezone

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config import Config
from database import Database
from services.user_service import UserService
from services.referral_service import ReferralService

async def test_referral_consistency():
    """Test that referral data is consistent across different display sections"""
    
    print("🔍 Testing Referral Data Consistency...")
    print("=" * 50)
    
    # Initialize database and services
    db = Database()
    await db.connect()
    
    user_service = UserService(db.db)
    referral_service = ReferralService(db.db)
    
    try:
        # Get a sample user with referrals (if any exist)
        users_cursor = db.db.users.find({'successful_referrals': {'$gt': 0}}).limit(5)
        test_users = await users_cursor.to_list(5)
        
        if not test_users:
            print("❌ No users with referrals found in database")
            print("💡 Create some test referrals first to test consistency")
            return
        
        print(f"📊 Testing {len(test_users)} users with referrals...")
        print()
        
        for user_data in test_users:
            user_id = user_data['user_id']
            user_name = user_data.get('first_name', f'User {user_id}')
            
            print(f"👤 Testing User: {user_name} (ID: {user_id})")
            print("-" * 30)
            
            # Method 1: From User model (used in balance profile)
            user_model_successful = user_data.get('successful_referrals', 0)
            user_model_referral_count = user_data.get('referral_count', 0)
            
            # Method 2: From Referral Service (used in referrals section)
            service_successful = await referral_service.get_successful_referral_count(user_id)
            service_total = await referral_service.get_referral_count(user_id)
            service_earnings = await referral_service.get_referral_earnings(user_id)
            
            print(f"📋 Balance Profile Data (User Model):")
            print(f"   • Successful Referrals: {user_model_successful}")
            print(f"   • Referral Count: {user_model_referral_count}")
            print(f"   • Calculated Earnings: 💎{user_model_successful * Config.REFERRAL_REWARD}")
            
            print(f"📋 Referrals Section Data (Service):")
            print(f"   • Successful Referrals: {service_successful}")
            print(f"   • Total Referrals: {service_total}")
            print(f"   • Actual Earnings: 💎{int(service_earnings)}")
            
            # Check consistency
            successful_match = user_model_successful == service_successful
            earnings_match = (user_model_successful * Config.REFERRAL_REWARD) == service_earnings
            
            print(f"✅ Consistency Check:")
            print(f"   • Successful Referrals Match: {'✅ YES' if successful_match else '❌ NO'}")
            print(f"   • Earnings Match: {'✅ YES' if earnings_match else '❌ NO'}")
            
            if not successful_match or not earnings_match:
                print(f"⚠️  INCONSISTENCY DETECTED!")
                print(f"   • User Model: {user_model_successful} referrals, 💎{user_model_successful * Config.REFERRAL_REWARD} earnings")
                print(f"   • Service: {service_successful} referrals, 💎{int(service_earnings)} earnings")
            
            print()
        
        print("🎯 Summary:")
        print("The fix ensures both sections use the same data source:")
        print("• Balance profile now uses referral_service.get_successful_referral_count()")
        print("• Referrals section already uses referral_service.get_successful_referral_count()")
        print("• Both sections should now show identical data")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        
    finally:
        await db.close()

async def show_referral_service_methods():
    """Show the different referral service methods and their purposes"""
    
    print("\n📚 Referral Service Methods:")
    print("=" * 50)
    print("• get_referral_count(user_id) - Total referrals (pending + completed)")
    print("• get_successful_referral_count(user_id) - Only completed referrals")
    print("• get_referral_earnings(user_id) - Total earnings from completed referrals")
    print()
    print("🔧 Fix Applied:")
    print("• Changed referral_service.complete_referral() to increment 'successful_referrals' instead of 'referral_count'")
    print("• Updated balance profile to use referral_service.get_successful_referral_count()")
    print("• Updated callback handler to use consistent methods")
    print("• Updated notification message to use current successful referral count")

if __name__ == "__main__":
    print("🚀 Referral Consistency Test")
    print("Testing the fix for referral data inconsistency between balance profile and referrals section")
    print()
    
    asyncio.run(test_referral_consistency())
    asyncio.run(show_referral_service_methods())
