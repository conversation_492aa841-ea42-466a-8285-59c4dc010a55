2025-07-11 16:33:42 - __main__ - INFO - main:6100 - 👋 <PERSON><PERSON> stopped by user
2025-07-11 16:33:54 - __main__ - INFO - main:6049 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:33:54 - __main__ - INFO - main:6050 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:33:54 - __main__ - INFO - main:6054 - ✅ Configuration validated
2025-07-11 16:33:55 - __main__ - INFO - main:6078 - ✅ All handlers added successfully
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:33:55 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 16:34:09 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6083 - 🚀 Starting bot in long polling mode...
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6084 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6085 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 16:34:09 - __main__ - INFO - init_and_run:6086 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 16:36:00 - __main__ - ERROR - _show_task_list:4518 - Error showing task list: Can't parse entities: can't find end of the entity starting at byte offset 132
2025-07-11 16:40:59 - __main__ - INFO - _show_task_creation_summary:4691 - Task creation summary data: {'step': 'reward', 'button_name': 'BOT OPENING', 'task_type': 'submit_image', 'reference_image_url': 'AgACAgUAAxkBAAIChmhw6ZD770sTm2WG4pNHM98jOYeQAAL6xjEbPh2JV066fQVu-U95AQADAgADeAADNgQ', 'instructions': 'HID\nfdsfAS', 'reward': 100.0}
2025-07-11 16:41:20 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:41:23 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:50:06 - __main__ - ERROR - _start_image_submission:5056 - Error starting image submission: There is no text in the message to edit
2025-07-11 16:56:37 - __main__ - INFO - main:6183 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:56:37 - __main__ - INFO - main:6184 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:56:37 - __main__ - INFO - main:6188 - ✅ Configuration validated
2025-07-11 16:56:38 - __main__ - INFO - main:6212 - ✅ All handlers added successfully
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 16:56:38 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 16:56:52 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6217 - 🚀 Starting bot in long polling mode...
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6218 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6219 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 16:56:52 - __main__ - INFO - init_and_run:6220 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 16:56:53 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:56:58 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:56:59 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:04 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:05 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:11 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:15 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:18 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:22 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:25 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:30 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:36 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:39 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:47 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:57:51 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:58:03 - __main__ - ERROR - error_handler:6032 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 16:58:08 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:01:13 - __main__ - INFO - main:6183 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 17:01:13 - __main__ - INFO - main:6184 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 17:01:13 - __main__ - INFO - main:6188 - ✅ Configuration validated
2025-07-11 17:01:14 - __main__ - INFO - main:6212 - ✅ All handlers added successfully
2025-07-11 17:01:14 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 17:01:14 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 17:01:14 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 17:01:28 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 17:01:28 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 17:01:28 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 17:01:28 - __main__ - INFO - init_and_run:6217 - 🚀 Starting bot in long polling mode...
2025-07-11 17:01:28 - __main__ - INFO - init_and_run:6218 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 17:01:28 - __main__ - INFO - init_and_run:6219 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 17:01:28 - __main__ - INFO - init_and_run:6220 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 17:02:21 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 1381431908
2025-07-11 17:02:21 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1001296547211: member
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1001296547211: member
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:220 - User 1381431908 status in channel -1002414699235: member
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:234 - ✅ User 1381431908 accepted - valid status in channel -1002414699235: member
2025-07-11 17:02:22 - __main__ - INFO - _verify_required_channels:260 - ✅ User 1381431908 verified as member of all required channels
2025-07-11 17:07:17 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-11 17:07:17 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1001296547211: administrator
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1002414699235: creator
2025-07-11 17:07:18 - __main__ - INFO - _verify_required_channels:260 - ✅ User 8153676253 verified as member of all required channels
2025-07-11 17:21:13 - __main__ - INFO - main:6452 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 17:21:13 - __main__ - INFO - main:6453 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 17:21:13 - __main__ - INFO - main:6457 - ✅ Configuration validated
2025-07-11 17:21:14 - __main__ - INFO - main:6481 - ✅ All handlers added successfully
2025-07-11 17:21:14 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-11 17:21:14 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-11 17:21:14 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-11 17:21:29 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-11 17:21:29 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-11 17:21:29 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-11 17:21:29 - __main__ - INFO - init_and_run:6486 - 🚀 Starting bot in long polling mode...
2025-07-11 17:21:29 - __main__ - INFO - init_and_run:6487 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-11 17:21:29 - __main__ - INFO - init_and_run:6488 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-11 17:21:29 - __main__ - INFO - init_and_run:6489 - 🤖 Bot username: @pro_gifts_bot
2025-07-11 17:21:30 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:35 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:36 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:40 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:42 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:47 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:49 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:21:57 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:03 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:12 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:22:17 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:27:03 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:03 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:04 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:04 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:05 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:05 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:27:07 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:31:57 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:00 - __main__ - ERROR - error_handler:6166 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:00 - __main__ - ERROR - error_handler:6435 - Update None caused error httpx.ConnectError: [Errno 11001] getaddrinfo failed
2025-07-11 17:32:06 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:32:10 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-11 17:32:18 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:15:59 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:03 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:04 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:09 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:11 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:17 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:19 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:23 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:26 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:32 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:38 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:46 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:16:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:17:06 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:17:11 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:19:53 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:19:58 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:20:24 - __main__ - ERROR - error_handler:6166 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:20:28 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:22:16 - __main__ - INFO - main:6452 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 06:22:16 - __main__ - INFO - main:6453 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 06:22:16 - __main__ - INFO - main:6457 - ✅ Configuration validated
2025-07-12 06:22:17 - __main__ - INFO - main:6481 - ✅ All handlers added successfully
2025-07-12 06:22:17 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 06:22:17 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 06:22:17 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 06:22:30 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 06:22:30 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 06:22:30 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 06:22:30 - __main__ - INFO - init_and_run:6486 - 🚀 Starting bot in long polling mode...
2025-07-12 06:22:30 - __main__ - INFO - init_and_run:6487 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 06:22:30 - __main__ - INFO - init_and_run:6488 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 06:22:30 - __main__ - INFO - init_and_run:6489 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 06:26:29 - __main__ - INFO - _handle_purchase_approval:3015 - Processing approval for user 1049516929, product type: 'canva_pro'
2025-07-12 06:26:29 - __main__ - INFO - _log_purchase_approval:3105 - Logging purchase approval for user 1049516929, product canva_pro to channel -1002712397029
2025-07-12 06:26:30 - __main__ - INFO - _log_purchase_approval:3165 - Purchase approval logged for user 1049516929, product canva_pro
2025-07-12 06:49:32 - __main__ - INFO - main:6462 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 06:49:32 - __main__ - INFO - main:6463 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 06:49:32 - __main__ - INFO - main:6467 - ✅ Configuration validated
2025-07-12 06:49:32 - __main__ - INFO - main:6472 - ✅ Daily bonus range: 💎8.0 - 💎20.0
2025-07-12 06:49:33 - __main__ - INFO - main:6499 - ✅ All handlers added successfully
2025-07-12 06:49:33 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 06:49:33 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 06:49:33 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 06:49:47 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 06:49:47 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 06:49:48 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 06:49:48 - __main__ - INFO - init_and_run:6504 - 🚀 Starting bot in long polling mode...
2025-07-12 06:49:48 - __main__ - INFO - init_and_run:6505 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 06:49:48 - __main__ - INFO - init_and_run:6506 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 06:49:48 - __main__ - INFO - init_and_run:6507 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 06:49:49 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:53 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:49:59 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:01 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:06 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:09 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:12 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:16 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:22 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:27 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:35 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:44 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:50:56 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:00 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:17 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:22 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:48 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:51:52 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:23 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:27 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:52:58 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:02 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:33 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:53:37 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:08 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:12 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:42 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:54:47 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:17 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:21 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:52 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:55:56 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:56:27 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:56:31 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:02 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:06 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:37 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:57:41 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:11 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:16 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:46 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:58:50 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:21 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:25 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 06:59:56 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:00 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:31 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:00:35 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:05 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:10 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:40 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:01:44 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:15 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:19 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:50 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:02:54 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:03:25 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:03:29 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:00 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:04 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:34 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:04:39 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:09 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:13 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:44 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:05:48 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:06:18 - __main__ - ERROR - handle_photo:791 - Error handling photo: 'NoneType' object has no attribute 'id'
2025-07-12 07:06:18 - __main__ - ERROR - error_handler:6445 - Update Update(channel_post=Message(caption='💰 DAILY CRYPTO UPDATE: 12-07-2025\n\nMajor Cryptocurrencies:\n\n- 🟠 BTC: $117,703.00 | 24h: +1.53% 📈\n\n- 🔷 ETH: $2,965.74 | 24h: +0.59% 📈\n\n- 🟣 SOL: $163.47 | 24h: -0.63% 📉\n\n- 💎 TON: $2.98 | 24h: +1.20% 📈\n\nMarket Highlights:\n\n- 🔥 Trending: #TON at $2.98 | 24h: +1.20%\n\n- 📈 Top Gainer: #M at $0.80 | 24h: +81.18%\n\n- 📉 Top Loser: #BONK at $0.00 | 24h: -6.73%', caption_entities=(MessageEntity(length=31, offset=3, type=<MessageEntityType.BOLD>), MessageEntity(length=23, offset=36, type=<MessageEntityType.ITALIC>), MessageEntity(length=36, offset=63, type=<MessageEntityType.ITALIC>), MessageEntity(length=34, offset=103, type=<MessageEntityType.ITALIC>), MessageEntity(length=32, offset=141, type=<MessageEntityType.ITALIC>), MessageEntity(length=30, offset=177, type=<MessageEntityType.ITALIC>), MessageEntity(length=18, offset=209, type=<MessageEntityType.ITALIC>), MessageEntity(length=13, offset=231, type=<MessageEntityType.ITALIC>), MessageEntity(length=4, offset=244, type=<MessageEntityType.HASHTAG>), MessageEntity(length=4, offset=244, type=<MessageEntityType.ITALIC>), MessageEntity(length=23, offset=248, type=<MessageEntityType.ITALIC>), MessageEntity(length=15, offset=275, type=<MessageEntityType.ITALIC>), MessageEntity(length=2, offset=290, type=<MessageEntityType.HASHTAG>), MessageEntity(length=2, offset=290, type=<MessageEntityType.ITALIC>), MessageEntity(length=24, offset=292, type=<MessageEntityType.ITALIC>), MessageEntity(length=14, offset=320, type=<MessageEntityType.ITALIC>), MessageEntity(length=5, offset=334, type=<MessageEntityType.HASHTAG>), MessageEntity(length=5, offset=334, type=<MessageEntityType.ITALIC>), MessageEntity(length=23, offset=339, type=<MessageEntityType.ITALIC>)), channel_chat_created=False, chat=Chat(id=-1001675894903, title="Dev's Crypto Channel 🪐", type=<ChatType.CHANNEL>, username='crypto_by_dev'), date=datetime.datetime(2025, 7, 12, 1, 36, 17, tzinfo=<UTC>), delete_chat_photo=False, group_chat_created=False, message_id=220, photo=(PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADcwADNgQ', file_size=1808, file_unique_id='AQADssgxG-iwUFd4', height=90, width=90), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADbQADNgQ', file_size=22519, file_unique_id='AQADssgxG-iwUFdy', height=320, width=320), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADeAADNgQ', file_size=82164, file_unique_id='AQADssgxG-iwUFd9', height=800, width=800), PhotoSize(file_id='AgACAgUAAx0CY-QgdwAD3GhxvBGI-P2X4Prnl1WND76Uh4dfAAKyyDEb6LBQV54RRAEEXuMWAQADAgADeQADNgQ', file_size=104421, file_unique_id='AQADssgxG-iwUFd-', height=1024, width=1024)), sender_chat=Chat(id=-1001675894903, title="Dev's Crypto Channel 🪐", type=<ChatType.CHANNEL>, username='crypto_by_dev'), supergroup_chat_created=False), update_id=388447795) caused error 'NoneType' object has no attribute 'reply_text'
2025-07-12 07:06:19 - __main__ - ERROR - error_handler:6445 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:06:23 - __main__ - ERROR - error_handler:6435 - Update None caused error Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-07-12 07:22:32 - __main__ - INFO - main:6462 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 07:22:32 - __main__ - INFO - main:6463 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 07:22:32 - __main__ - INFO - main:6467 - ✅ Configuration validated
2025-07-12 07:22:32 - __main__ - INFO - main:6472 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 07:22:33 - __main__ - INFO - main:6499 - ✅ All handlers added successfully
2025-07-12 07:22:33 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 07:22:33 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 07:22:33 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 07:22:47 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 07:22:47 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 07:22:47 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 07:22:47 - __main__ - INFO - init_and_run:6504 - 🚀 Starting bot in long polling mode...
2025-07-12 07:22:47 - __main__ - INFO - init_and_run:6505 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 07:22:47 - __main__ - INFO - init_and_run:6506 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 07:22:47 - __main__ - INFO - init_and_run:6507 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 07:51:44 - __main__ - INFO - main:6462 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 07:51:44 - __main__ - INFO - main:6463 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 07:51:44 - __main__ - INFO - main:6467 - ✅ Configuration validated
2025-07-12 07:51:44 - __main__ - INFO - main:6472 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 07:51:45 - __main__ - INFO - main:6499 - ✅ All handlers added successfully
2025-07-12 07:51:45 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 07:51:45 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 07:51:45 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 07:52:00 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 07:52:00 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 07:52:00 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 07:52:00 - __main__ - INFO - init_and_run:6504 - 🚀 Starting bot in long polling mode...
2025-07-12 07:52:00 - __main__ - INFO - init_and_run:6505 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 07:52:00 - __main__ - INFO - init_and_run:6506 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 07:52:00 - __main__ - INFO - init_and_run:6507 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 07:56:05 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 8153676253
2025-07-12 07:56:05 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-12 07:56:06 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1001296547211: administrator
2025-07-12 07:56:06 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1001296547211: administrator
2025-07-12 07:56:06 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-12 07:56:07 - __main__ - INFO - _verify_required_channels:220 - User 8153676253 status in channel -1002414699235: creator
2025-07-12 07:56:07 - __main__ - INFO - _verify_required_channels:234 - ✅ User 8153676253 accepted - valid status in channel -1002414699235: creator
2025-07-12 07:56:07 - __main__ - INFO - _verify_required_channels:260 - ✅ User 8153676253 verified as member of all required channels
2025-07-12 08:06:09 - __main__ - INFO - main:6466 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:06:09 - __main__ - INFO - main:6467 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:06:09 - __main__ - INFO - main:6471 - ✅ Configuration validated
2025-07-12 08:06:09 - __main__ - INFO - main:6476 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:06:10 - __main__ - INFO - main:6503 - ✅ All handlers added successfully
2025-07-12 08:06:10 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:06:10 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:06:10 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:06:24 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:06:24 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:06:24 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:06:24 - __main__ - INFO - init_and_run:6508 - 🚀 Starting bot in long polling mode...
2025-07-12 08:06:24 - __main__ - INFO - init_and_run:6509 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:06:24 - __main__ - INFO - init_and_run:6510 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:06:24 - __main__ - INFO - init_and_run:6511 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:07:44 - __main__ - INFO - main:6466 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:07:44 - __main__ - INFO - main:6467 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:07:44 - __main__ - INFO - main:6471 - ✅ Configuration validated
2025-07-12 08:07:44 - __main__ - INFO - main:6476 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:07:44 - __main__ - INFO - main:6503 - ✅ All handlers added successfully
2025-07-12 08:07:44 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:07:44 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:07:44 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:07:59 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:07:59 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:07:59 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:07:59 - __main__ - INFO - init_and_run:6508 - 🚀 Starting bot in long polling mode...
2025-07-12 08:07:59 - __main__ - INFO - init_and_run:6509 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:07:59 - __main__ - INFO - init_and_run:6510 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:07:59 - __main__ - INFO - init_and_run:6511 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:09:04 - __main__ - INFO - main:6466 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:09:04 - __main__ - INFO - main:6467 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:09:04 - __main__ - INFO - main:6471 - ✅ Configuration validated
2025-07-12 08:09:04 - __main__ - INFO - main:6476 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:09:04 - __main__ - INFO - main:6503 - ✅ All handlers added successfully
2025-07-12 08:09:04 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:09:04 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:09:04 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:09:20 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:09:20 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:09:20 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:09:20 - __main__ - INFO - init_and_run:6508 - 🚀 Starting bot in long polling mode...
2025-07-12 08:09:20 - __main__ - INFO - init_and_run:6509 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:09:20 - __main__ - INFO - init_and_run:6510 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:09:20 - __main__ - INFO - init_and_run:6511 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:10:20 - __main__ - INFO - main:6466 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:10:20 - __main__ - INFO - main:6467 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:10:20 - __main__ - INFO - main:6471 - ✅ Configuration validated
2025-07-12 08:10:20 - __main__ - INFO - main:6476 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:10:20 - __main__ - INFO - main:6503 - ✅ All handlers added successfully
2025-07-12 08:10:20 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:10:20 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:10:20 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:10:35 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:10:35 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:10:35 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:10:35 - __main__ - INFO - init_and_run:6508 - 🚀 Starting bot in long polling mode...
2025-07-12 08:10:35 - __main__ - INFO - init_and_run:6509 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:10:35 - __main__ - INFO - init_and_run:6510 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:10:35 - __main__ - INFO - init_and_run:6511 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:14:04 - __main__ - INFO - main:6468 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:14:04 - __main__ - INFO - main:6469 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:14:04 - __main__ - INFO - main:6473 - ✅ Configuration validated
2025-07-12 08:14:04 - __main__ - INFO - main:6478 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:14:05 - __main__ - INFO - main:6505 - ✅ All handlers added successfully
2025-07-12 08:14:05 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:14:05 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:14:05 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:14:13 - __main__ - INFO - main:6527 - 👋 Bot stopped by user
2025-07-12 08:14:18 - __main__ - INFO - main:6468 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:14:18 - __main__ - INFO - main:6469 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:14:18 - __main__ - INFO - main:6473 - ✅ Configuration validated
2025-07-12 08:14:18 - __main__ - INFO - main:6478 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:14:19 - __main__ - INFO - main:6505 - ✅ All handlers added successfully
2025-07-12 08:14:19 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:14:19 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:14:19 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:14:32 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:14:32 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:14:32 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:14:32 - __main__ - INFO - init_and_run:6510 - 🚀 Starting bot in long polling mode...
2025-07-12 08:14:32 - __main__ - INFO - init_and_run:6511 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:14:32 - __main__ - INFO - init_and_run:6512 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:14:32 - __main__ - INFO - init_and_run:6513 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:21:06 - __main__ - INFO - main:6527 - 👋 Bot stopped by user
2025-07-12 08:21:11 - __main__ - INFO - main:6468 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:21:11 - __main__ - INFO - main:6469 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:21:11 - __main__ - INFO - main:6473 - ✅ Configuration validated
2025-07-12 08:21:11 - __main__ - INFO - main:6478 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:21:11 - __main__ - INFO - main:6505 - ✅ All handlers added successfully
2025-07-12 08:21:11 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:21:11 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:21:11 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:21:25 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:21:25 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:21:25 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:21:25 - __main__ - INFO - init_and_run:6510 - 🚀 Starting bot in long polling mode...
2025-07-12 08:21:25 - __main__ - INFO - init_and_run:6511 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:21:25 - __main__ - INFO - init_and_run:6512 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:21:25 - __main__ - INFO - init_and_run:6513 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:29:03 - __main__ - INFO - main:6470 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:29:03 - __main__ - INFO - main:6471 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:29:03 - __main__ - INFO - main:6475 - ✅ Configuration validated
2025-07-12 08:29:03 - __main__ - INFO - main:6480 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:29:04 - __main__ - INFO - main:6507 - ✅ All handlers added successfully
2025-07-12 08:29:04 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:29:04 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:29:04 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:29:19 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:29:19 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:29:19 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:29:19 - __main__ - INFO - init_and_run:6512 - 🚀 Starting bot in long polling mode...
2025-07-12 08:29:19 - __main__ - INFO - init_and_run:6513 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:29:19 - __main__ - INFO - init_and_run:6514 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:29:19 - __main__ - INFO - init_and_run:6515 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:30:45 - __main__ - INFO - main:6470 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:30:45 - __main__ - INFO - main:6471 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:30:45 - __main__ - INFO - main:6475 - ✅ Configuration validated
2025-07-12 08:30:45 - __main__ - INFO - main:6480 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:30:46 - __main__ - INFO - main:6507 - ✅ All handlers added successfully
2025-07-12 08:30:46 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:30:46 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:30:46 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:31:10 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:31:10 - __main__ - ERROR - initialize_async_components:84 - ❌ Failed to initialize async components: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 08:31:10 - __main__ - ERROR - main:6531 - ❌ Bot error: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 08:32:39 - __main__ - INFO - main:6470 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:32:39 - __main__ - INFO - main:6471 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:32:39 - __main__ - INFO - main:6475 - ✅ Configuration validated
2025-07-12 08:32:39 - __main__ - INFO - main:6480 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:32:40 - __main__ - INFO - main:6507 - ✅ All handlers added successfully
2025-07-12 08:32:40 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:32:40 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:32:40 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:32:56 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:32:56 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:32:56 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:32:56 - __main__ - INFO - init_and_run:6512 - 🚀 Starting bot in long polling mode...
2025-07-12 08:32:56 - __main__ - INFO - init_and_run:6513 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:32:56 - __main__ - INFO - init_and_run:6514 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:32:56 - __main__ - INFO - init_and_run:6515 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:35:32 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 7464736260
2025-07-12 08:35:32 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-12 08:35:33 - __main__ - INFO - _verify_required_channels:220 - User 7464736260 status in channel -1001296547211: left
2025-07-12 08:35:33 - __main__ - INFO - _verify_required_channels:228 - ❌ User 7464736260 rejected - invalid status in channel -1001296547211: left
2025-07-12 08:35:33 - __main__ - INFO - _verify_required_channels:229 -    Invalid statuses: ['left', 'kicked', 'restricted']
2025-07-12 08:35:43 - __main__ - INFO - _handle_channel_verification_callback:3358 - Verification callback - Message type: text=False, caption=True
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:211 - Starting channel verification for user 7464736260
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 1: -1001296547211
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:220 - User 7464736260 status in channel -1001296547211: member
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:234 - ✅ User 7464736260 accepted - valid status in channel -1001296547211: member
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:215 - Checking membership in channel 2: -1002414699235
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:220 - User 7464736260 status in channel -1002414699235: member
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:234 - ✅ User 7464736260 accepted - valid status in channel -1002414699235: member
2025-07-12 08:35:45 - __main__ - INFO - _verify_required_channels:260 - ✅ User 7464736260 verified as member of all required channels
2025-07-12 08:35:47 - __main__ - WARNING - _handle_channel_verification_callback:3433 - Could not delete verification message
2025-07-12 08:35:51 - __main__ - INFO - _process_referral:3670 - Processed referral: 1049516929 -> 7464736260
2025-07-12 08:42:59 - __main__ - INFO - main:6477 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:42:59 - __main__ - INFO - main:6478 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:42:59 - __main__ - INFO - main:6482 - ✅ Configuration validated
2025-07-12 08:42:59 - __main__ - INFO - main:6487 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:43:00 - __main__ - INFO - main:6514 - ✅ All handlers added successfully
2025-07-12 08:43:00 - __main__ - INFO - initialize_async_components:48 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:43:00 - __main__ - INFO - initialize_async_components:49 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:43:00 - __main__ - INFO - initialize_async_components:53 - ✅ Configuration validated
2025-07-12 08:43:15 - __main__ - INFO - initialize_async_components:58 - ✅ Database connected successfully
2025-07-12 08:43:15 - __main__ - INFO - initialize_async_components:74 - ✅ Services initialized
2025-07-12 08:43:15 - __main__ - INFO - initialize_async_components:79 - ✅ Default products initialized
2025-07-12 08:43:15 - __main__ - INFO - init_and_run:6519 - 🚀 Starting bot in long polling mode...
2025-07-12 08:43:15 - __main__ - INFO - init_and_run:6520 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:43:15 - __main__ - INFO - init_and_run:6521 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:43:15 - __main__ - INFO - init_and_run:6522 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 08:56:57 - __main__ - INFO - main:6490 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:56:57 - __main__ - INFO - main:6491 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:56:57 - __main__ - INFO - main:6495 - ✅ Configuration validated
2025-07-12 08:56:57 - __main__ - INFO - main:6503 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 08:56:57 - __main__ - INFO - main:6530 - ✅ All handlers added successfully
2025-07-12 08:56:57 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 08:56:57 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 08:56:57 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 08:57:12 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 08:57:12 - __main__ - INFO - initialize_async_components:75 - ✅ Services initialized
2025-07-12 08:57:12 - __main__ - INFO - initialize_async_components:80 - ✅ Configuration monitoring initialized
2025-07-12 08:57:12 - __main__ - INFO - initialize_async_components:85 - ✅ Default products initialized
2025-07-12 08:57:12 - __main__ - INFO - init_and_run:6535 - 🚀 Starting bot in long polling mode...
2025-07-12 08:57:12 - __main__ - INFO - init_and_run:6536 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 08:57:12 - __main__ - INFO - init_and_run:6537 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 08:57:12 - __main__ - INFO - init_and_run:6538 - 🤖 Bot username: @pro_gifts_bot
2025-07-12 09:13:47 - __main__ - INFO - main:6507 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:13:47 - __main__ - INFO - main:6508 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:13:47 - __main__ - INFO - main:6512 - ✅ Configuration validated
2025-07-12 09:13:47 - __main__ - INFO - main:6520 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 09:13:48 - __main__ - INFO - main:6547 - ✅ All handlers added successfully
2025-07-12 09:13:48 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:13:48 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:13:48 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 09:14:08 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 09:14:08 - __main__ - ERROR - initialize_async_components:90 - ❌ Failed to initialize async components: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 09:14:08 - __main__ - ERROR - main:6571 - ❌ Bot error: 'NoneType' object has no attribute 'admin_settings'
2025-07-12 09:14:27 - __main__ - INFO - main:6507 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:14:27 - __main__ - INFO - main:6508 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:14:27 - __main__ - INFO - main:6512 - ✅ Configuration validated
2025-07-12 09:14:27 - __main__ - INFO - main:6520 - ✅ Daily bonus range: 💎8 - 💎20
2025-07-12 09:14:28 - __main__ - INFO - main:6547 - ✅ All handlers added successfully
2025-07-12 09:14:28 - __main__ - INFO - initialize_async_components:49 - 🤖 Starting Complete Telegram Referral Earning Bot...
2025-07-12 09:14:28 - __main__ - INFO - initialize_async_components:50 - 🔄 Mode: Long Polling (no webhook/domain required)
2025-07-12 09:14:28 - __main__ - INFO - initialize_async_components:54 - ✅ Configuration validated
2025-07-12 09:14:48 - __main__ - INFO - initialize_async_components:59 - ✅ Database connected successfully
2025-07-12 09:14:48 - __main__ - INFO - initialize_async_components:75 - ✅ Services initialized
2025-07-12 09:14:48 - __main__ - INFO - initialize_async_components:80 - ✅ Configuration monitoring initialized
2025-07-12 09:14:48 - __main__ - INFO - initialize_async_components:85 - ✅ Default products initialized
2025-07-12 09:14:48 - __main__ - INFO - init_and_run:6552 - 🚀 Starting bot in long polling mode...
2025-07-12 09:14:48 - __main__ - INFO - init_and_run:6553 - ✅ Bot is running! Press Ctrl+C to stop.
2025-07-12 09:14:48 - __main__ - INFO - init_and_run:6554 - ℹ️ No webhook, domain, or SSL configuration required.
2025-07-12 09:14:48 - __main__ - INFO - init_and_run:6555 - 🤖 Bot username: @pro_gifts_bot
